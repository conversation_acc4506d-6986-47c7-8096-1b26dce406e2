import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlong;
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

class SimpleMapPage extends StatefulWidget {
  const SimpleMapPage({super.key});

  @override
  State<SimpleMapPage> createState() => _SimpleMapPageState();
}

class _SimpleMapPageState extends State<SimpleMapPage> {
  MapController mapController = MapController();
  latlong.LatLng selectedLocation = latlong.LatLng(24.7136, 46.6753); // الرياض
  String selectedAddress = '';
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🗺️ اختيار الموقع'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _getCurrentLocation,
            icon: Icon(Icons.my_location),
            tooltip: 'موقعي الحالي',
          ),
        ],
      ),
      body: Stack(
        children: [
          // الخريطة
          FlutterMap(
            mapController: mapController,
            options: MapOptions(
              initialCenter: selectedLocation,
              initialZoom: 15.0,
              onTap: (tapPosition, point) {
                setState(() {
                  selectedLocation = point;
                });
                _getAddressFromCoordinates(point);
              },
            ),
            children: [
              // طبقة الخريطة
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.example.shop_app',
                maxZoom: 19,
              ),
              // طبقة العلامات
              MarkerLayer(
                markers: [
                  Marker(
                    point: selectedLocation,
                    width: 50,
                    height: 50,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 6,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.location_on,
                        color: Colors.white,
                        size: 25,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          // معلومات الموقع
          if (selectedAddress.isNotEmpty)
            Positioned(
              top: 16,
              left: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'الموقع المحدد',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      selectedAddress,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // مؤشر التحميل
          if (isLoading)
            Container(
              color: Colors.black.withValues(alpha: 0.3),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(color: Colors.green),
                      const SizedBox(height: 16),
                      Text('جاري تحديد الموقع...'),
                    ],
                  ),
                ),
              ),
            ),

          // أزرار التحكم
          Positioned(
            right: 16,
            bottom: 100,
            child: Column(
              children: [
                FloatingActionButton.small(
                  heroTag: "zoom_in",
                  onPressed: () {
                    mapController.move(selectedLocation, mapController.camera.zoom + 1);
                  },
                  backgroundColor: Colors.white,
                  child: Icon(Icons.add, color: Colors.green),
                ),
                const SizedBox(height: 8),
                FloatingActionButton.small(
                  heroTag: "zoom_out",
                  onPressed: () {
                    mapController.move(selectedLocation, mapController.camera.zoom - 1);
                  },
                  backgroundColor: Colors.white,
                  child: Icon(Icons.remove, color: Colors.green),
                ),
              ],
            ),
          ),
        ],
      ),

      // زر تأكيد الموقع
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: ElevatedButton(
            onPressed: selectedAddress.isNotEmpty ? _confirmLocation : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.check_circle, size: 20),
                const SizedBox(width: 8),
                Text(
                  'تأكيد الموقع',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // الحصول على الموقع الحالي
  void _getCurrentLocation() async {
    setState(() {
      isLoading = true;
    });

    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
        Get.snackbar('خطأ', 'صلاحية الموقع مطلوبة');
        return;
      }

      Position position = await Geolocator.getCurrentPosition();
      latlong.LatLng currentLocation = latlong.LatLng(position.latitude, position.longitude);
      
      setState(() {
        selectedLocation = currentLocation;
      });
      
      mapController.move(currentLocation, 16.0);
      _getAddressFromCoordinates(currentLocation);

    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ في الحصول على الموقع');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // تحويل الإحداثيات إلى عنوان
  void _getAddressFromCoordinates(latlong.LatLng location) async {
    setState(() {
      isLoading = true;
    });

    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        List<String> addressParts = [];
        
        if (place.street != null && place.street!.isNotEmpty) {
          addressParts.add(place.street!);
        }
        if (place.locality != null && place.locality!.isNotEmpty) {
          addressParts.add(place.locality!);
        }
        if (place.country != null && place.country!.isNotEmpty) {
          addressParts.add(place.country!);
        }

        setState(() {
          selectedAddress = addressParts.join(', ');
        });
      }
    } catch (e) {
      setState(() {
        selectedAddress = 'خطأ في تحديد العنوان';
      });
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // تأكيد الموقع
  void _confirmLocation() {
    Map<String, dynamic> result = {
      'latitude': selectedLocation.latitude,
      'longitude': selectedLocation.longitude,
      'address': selectedAddress,
    };
    
    Get.back(result: result);
    Get.snackbar(
      'تم بنجاح',
      'تم حفظ الموقع: $selectedAddress',
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }
}
