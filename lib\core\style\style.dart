import 'package:flutter/material.dart';
import 'package:shop_app/core/style/repo.dart';

class AppStyle {
  static ThemeData get theme {
    TextTheme textTheme = const TextTheme(
      headlineSmall: TextStyle(fontWeight: FontWeight.w700, fontSize: 24),
      titleLarge: TextStyle(fontWeight: FontWeight.w700, fontSize: 22),
      titleMedium: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w700,
      ),
      titleSmall: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w700,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w700,
      ),
      labelMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      labelSmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w300,
      ),
    );
    return ThemeData(
      textTheme: textTheme,
      primaryColor: StyleRepo.green,
      appBarTheme: AppBarTheme(backgroundColor: StyleRepo.white),
      scaffoldBackgroundColor: StyleRepo.white,
      navigationBarTheme: NavigationBarThemeData(
        iconTheme: WidgetStateProperty.resolveWith(
          (states) {
            if (states.contains(WidgetState.selected)) {
              return IconThemeData(color: StyleRepo.green);
            }
            return IconThemeData(color: StyleRepo.darkGrey);
          },
        ),
      ),
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: StyleRepo.meduimGrey, // اللون فقط داخل الورقة
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
        ),
        //surfaceTintColor: StyleRepo.sandyBrown,
      ),
      tabBarTheme: TabBarTheme(
        labelColor: StyleRepo.green, // للتاب المحدد
        unselectedLabelColor: StyleRepo.darkGrey, // للتاب غير المحدد
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(
            width: 3.0,
            color: StyleRepo.green, // لون المؤشر (الخط السفلي)
          ),
        ),
        overlayColor: WidgetStateProperty.resolveWith(
          (states) {
            if (states.contains(WidgetState.selected)) {
              return StyleRepo.green;
            }
            return StyleRepo.darkGrey;
          },
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: StyleRepo.green,
          foregroundColor: StyleRepo.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          fixedSize: Size(250, 50),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: StyleRepo.meduimGrey,
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.circular(15),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.circular(15),
        ),
      ),
    );
  }
}
