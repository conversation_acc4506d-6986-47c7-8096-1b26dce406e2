import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:shop_app/core/config/app_builder.dart';

class LoginPageController extends GetxController {
  AppBuilder appBuilder = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  late TextEditingController phoneNumber, password;
  var passwordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
  );
  @override
  onInit() {
    phoneNumber = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    phoneNumber.dispose();
    password.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }
  }
}
