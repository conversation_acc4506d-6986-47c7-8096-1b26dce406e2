import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/style/repo.dart';

import '../../../models/product_details.dart';
import '../controller.dart';

class OptionWidget extends StatelessWidget {
  final int optionsListId;
  final ProductOption option;
  final void Function(ProductOption option) onChanged;

  const OptionWidget(
      {super.key,
      required this.option,
      required this.optionsListId,
      required this.onChanged});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OptionsListController>(tag: "$optionsListId");
    return InkWell(
      onTap: () {
        controller.selectedOption = option.id;
        onChanged(option);
      },
      child: Container(
        height: 50,
        padding: EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: StyleRepo.darkGrey),
        ),
        child: Row(
          children: [
            Obx(
              () => Radio(
                value: option.id,
                groupValue: controller.selectedOption,
                onChanged: (selected) {
                  controller.selectedOption = selected!;
                  onChanged(option);
                },
              ),
            ),
            Text(option.name),
            Spacer(),
            if (option.priceBeforeDiscount != null)
              Text(
                option.priceBeforeDiscount.toString(),
                style: TextStyle(
                  fontSize: 12,
                  color: StyleRepo.darkGrey,
                  decoration: TextDecoration.lineThrough,
                ),
              ),
            SizedBox(width: 8),
            Text(option.price.toString()),
          ],
        ),
      ),
    );
  }
}
