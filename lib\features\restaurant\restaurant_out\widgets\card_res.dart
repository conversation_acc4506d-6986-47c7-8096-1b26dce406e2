import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/demo/media.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';

class CardRestaurant extends StatelessWidget {
  const CardRestaurant({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * .4,
      decoration: BoxDecoration(
        color: StyleRepo.white,
        borderRadius: BorderRadius.circular(18),
        // border: Border.all(color: StyleRepo.lightGrey),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          textDirection: TextDirection.ltr,
          children: [
            InkWell(
              onTap: () {
                Get.toNamed(Pages.RestaurantIn.value);
              },
              child: AppImage(
                height: 150,
                width: double.infinity,
                path: DemoMedia.getAppRandomImage,
                type: ImageType.CachedNetwork,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
            Text(
              "Big Restaurant",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            Text(
              "explain Big Restaurant ",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              "Address Big Restaurant",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
