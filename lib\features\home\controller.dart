

import 'package:get/get.dart';
import 'package:shop_app/core/models/product/product.dart';

import '../../core/models/category.dart';
import '../../core/services/rest_api/rest_api.dart';
import '../../core/services/state_management/obs.dart';

class HomePageController extends GetxController {
  ObsList<MainProduct> products = ObsList([]);
  // متغير للتحكم في حالة البحث
  RxBool isSearching = false.obs;
  // متغير لتخزين نص البحث
  RxString searchQuery = ''.obs;
  // قائمة لتخزين نتائج البحث
  ObsList<MainProduct> searchResults = ObsList([]);

  fetchProducts() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.products,
        fromJson: MainProduct.fromJson,
      ),
    );

    if (response.success) {
      products.value = response.data;
    } else {
      products.error = response.message;
    }
  }
  //!SECTION

  //SECTION - Categories
  ObsList<CategoryModel> categories = ObsList([]);

  fetchCategories() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.categories,
        fromJson: CategoryModel.fromJson,
        copyHeader: {"Accept-Language": "en"},
      ),
    );

    if (response.success) {
      categories.value = response.data;
    } else {
      categories.error = response.message;
    }
  }
  //!SECTION

  // SECTION - Search
  /// دالة للبحث عن المنتجات من خلال API
  /// 
  /// تقوم هذه الدالة بإرسال طلب بحث إلى الخادم باستخدام النص المدخل
  /// وتقوم بتحديث قائمة نتائج البحث عند استلام الرد
  /// 
  /// @param query نص البحث المراد البحث عنه
  searchProducts(String query) async {
    // تحديث حالة البحث ونص البحث
    isSearching.value = true;
    searchQuery.value = query;
    
    // إذا كان نص البحث فارغًا، نقوم بإفراغ نتائج البحث والخروج
    if (query.isEmpty) {
      searchResults.value = [];
      isSearching.value = false;
      return;
    }
    
   
    
    try {
      // إرسال طلب البحث إلى API
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.products,
          params: {"search": query},
          fromJson: MainProduct.fromJson,
        ),
      );

      // التحقق من نجاح الطلب
      if (response.success) {
        // تحديث نتائج البحث
        searchResults.value = response.data;
      } else {
        // تعيين رسالة الخطأ في حالة فشل الطلب
        searchResults.error = response.message;
      }
    } catch (e) {
      // التعامل مع الأخطاء غير المتوقعة
      searchResults.error = "حدث خطأ أثناء البحث";
    } 
  }

  /// دالة للبحث المحلي في المنتجات المحملة مسبقًا
  /// 
  /// تستخدم هذه الدالة عندما نريد البحث في البيانات المحملة بالفعل
  /// بدلاً من إرسال طلب جديد إلى الخادم
  /// 
  /// @param query نص البحث المراد البحث عنه
  searchProductsLocally(String query) {
    isSearching.value = true;
    searchQuery.value = query;
    
    if (query.isEmpty) {
      searchResults.value = [];
      isSearching.value = false;
      return;
    }
    
    // البحث في المنتجات المحملة مسبقًا
    if (products.value != null) {
      searchResults.value = products.value!
          .where((product) => 
              product.name.toLowerCase().contains(query.toLowerCase()) ||
              product.description.toLowerCase().contains(query.toLowerCase()))
          .toList();
    } else {
      searchResults.value = [];
    }
  }

  /// دالة لإلغاء البحث والعودة إلى العرض العادي
  clearSearch() {
    isSearching.value = false;
    searchQuery.value = '';
    searchResults.value = [];
  }
  //!SECTION

  @override
  void onInit() {
    fetchProducts();
    fetchCategories();
    super.onInit();
  }
}