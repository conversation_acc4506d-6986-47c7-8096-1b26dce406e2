import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/products/categories/controller.dart';
import 'package:shop_app/features/products/categories/widgets/categorie_card.dart';

class CategoriesPage extends StatelessWidget {
  const CategoriesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CategoriesPageController());

    return Column(
      children: [
        SizedBox(height: 10 + MediaQuery.viewPaddingOf(context).top),
        Text(
          tr(LocaleKeys.Find_Products),
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        SizedBox(
          height: 5,
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Text<PERSON><PERSON><PERSON><PERSON>(
            decoration: InputDecoration(
              hintText: tr(LocaleKeys.Search_Store),
              prefixIcon: Icon(Icons.search),
            ),
          ),
        ),
        SizedBox(
          height: 10,
        ),
        ObsListBuilder(
          obs: controller.categories,
          builder: (context, categories) {
            return Expanded(
              child: GridView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                children: List.generate(
                  categories.length,
                  (index) {
                    final pair = StyleRepo
                        .colorPairs[index % StyleRepo.colorPairs.length];
                    return CategorieCard(
                      color_border: pair[0],
                      color_fill: pair[1],
                      Categoriesname: categories[index].name,
                    );
                  },
                ),
              ),
            );
          },
        ),
        SizedBox(
          height: 10,
        )
      ],
    );
  }
}
