import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/restaurant/restaurant_out/controller.dart';
import 'package:shop_app/features/restaurant/restaurant_out/widgets/card_res.dart';
import 'package:shop_app/gen/assets.gen.dart';

class RestaurantOutPage extends StatelessWidget {
  const RestaurantOutPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RestaurantOutPageController());
    return Scaffold(
      appBar: AppBar(
        title: Center(
          child: Text(
            "Restaurant",
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        automaticallyImplyLeading: false,
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(40),
          child: Row(
            children: [
              IconButton(
                icon: Assets.icons.back.svg(),
                onPressed: () {
                  Get.back();
                },
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width * .8,
                height: 50,
                child: TextField(
                  controller: controller.searchTextController,
                  decoration: InputDecoration(
                    prefixIcon: Icon(Icons.search, color: StyleRepo.black),
                    suffixIcon: IconButton(
                      icon: Assets.icons.closeSearch.svg(),
                      onPressed: () {
                        controller.clearSearch();
                      },
                    ),
                    hintText: 'Search Store',
                  ),
                ),
              ),
              SizedBox(width: 10),
            ],
          ),
        ),
      ),
      body: ListView.separated(
        itemCount: 15,
        separatorBuilder: (_, __) => SizedBox(
          height: 12,
        ),
        itemBuilder: (context, index) {
          return CardRestaurant();
        },
      ),
    );
  }
}
