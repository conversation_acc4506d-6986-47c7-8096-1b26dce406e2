// import 'dart:convert';

// import 'package:shop_app/core/models/product/product.dart';

// class ProductDetails extends MainProduct {
//   late List<String> images;
//   late bool isFavorited;
//   late List<ProductOptionsList> options;

//   ProductDetails.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
//     images = List<String>.from(json["images"].map((x) => x));
//     isFavorited = json["isFavorited"];
//     options = List<ProductOptionsList>.from(
//         json["options"].map((x) => ProductOptionsList.fromJson(x)));
//     // options = [
//     //   ...options,
//     //   ...options,
//     // ];
//   }

//   @override
//   Map<String, dynamic> toJson() {
//     Map<String, dynamic> data = super.toJson();
//     data.addAll({
//       "images": List<dynamic>.from(images.map((x) => x)),
//       "isFavorited": isFavorited,
//       "options": List<dynamic>.from(options.map((x) => x.toJson())),
//     });

//     return data;
//   }
// }

// class ProductOptionsList {
//   int optionDefault;
//   bool replacePrice;
//   List<ProductOption> options;

//   ProductOptionsList({
//     required this.optionDefault,
//     required this.replacePrice,
//     required this.options,
//   });

//   factory ProductOptionsList.fromRawJson(String str) =>
//       ProductOptionsList.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory ProductOptionsList.fromJson(Map<String, dynamic> json) =>
//       ProductOptionsList(
//         optionDefault: json["default"],
//         replacePrice: json["replace_price"],
//         options: List<ProductOption>.from(
//             json["options"].map((x) => ProductOption.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "default": optionDefault,
//         "replace_price": replacePrice,
//         "options": List<dynamic>.from(options.map((x) => x.toJson())),
//       };
// }

// class ProductOption {
//   int id;
//   String name;
//   int? priceBeforeDiscount;
//   int price;

//   ProductOption({
//     required this.id,
//     required this.name,
//     this.priceBeforeDiscount,
//     required this.price,
//   });

//   factory ProductOption.fromRawJson(String str) =>
//       ProductOption.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory ProductOption.fromJson(Map<String, dynamic> json) => ProductOption(
//         id: json["id"],
//         name: json["name"],
//         priceBeforeDiscount: json["price_before_discount"],
//         price: json["price"],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "name": name,
//         "price_before_discount": priceBeforeDiscount,
//         "price": price,
//       };
// }
