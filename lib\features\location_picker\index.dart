import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'controller.dart';

class LocationPickerPage extends StatelessWidget {
  const LocationPickerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LocationPickerController>(
      init: LocationPickerController(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: Text(tr(LocaleKeys.location_picker)),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            actions: [
              // زر الموقع الحالي
              IconButton(
                onPressed: controller.getCurrentLocation,
                icon: Icon(Icons.my_location),
                tooltip: tr(LocaleKeys.my_location),
              ),
            ],
          ),
          body: Stack(
            children: [
              // خريطة Google Maps
              Obx(() => GoogleMap(
                    onMapCreated: controller.onMapCreated,
                    initialCameraPosition: CameraPosition(
                      target: controller.selectedLocation.value,
                      zoom: 15.0,
                    ),
                    onTap: controller.onMapTapped,
                    markers: controller.markers.toSet(),
                    myLocationEnabled: true,
                    myLocationButtonEnabled: false,
                    zoomControlsEnabled: true,
                    mapToolbarEnabled: false,
                  )),

              // معلومات الموقع المحدد
              Positioned(
                top: 16,
                left: 16,
                right: 16,
                child: Obx(
                  () => (controller.selectedLocation.value.latitude != 0.0 ||
                          controller.selectedLocation.value.longitude != 0.0)
                      ? Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.location_on,
                                      color: Colors.green, size: 20),
                                  const SizedBox(width: 8),
                                  Text(
                                    tr(LocaleKeys.selected_location),
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                controller.selectedAddress.value.isNotEmpty
                                    ? controller.selectedAddress.value
                                    : 'موقع محدد: ${controller.selectedLocation.value.latitude.toStringAsFixed(6)}, ${controller.selectedLocation.value.longitude.toStringAsFixed(6)}',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ],
                          ),
                        )
                      : SizedBox.shrink(),
                ),
              ),

              // مؤشر التحميل
              Obx(
                () => controller.isLoading.value
                    ? Container(
                        color: Colors.black.withValues(alpha: 0.3),
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CircularProgressIndicator(color: Colors.green),
                                const SizedBox(height: 16),
                                Text(tr(LocaleKeys.loading_location)),
                              ],
                            ),
                          ),
                        ),
                      )
                    : SizedBox.shrink(),
              ),
            ],
          ),

          // زر تأكيد الموقع
          bottomNavigationBar: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: Obx(() => ElevatedButton(
                    onPressed: () => controller.confirmLocation(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          (controller.selectedLocation.value.latitude != 0.0 ||
                                  controller.selectedLocation.value.longitude !=
                                      0.0)
                              ? Colors.green
                              : Colors.grey,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation:
                          (controller.selectedLocation.value.latitude != 0.0 ||
                                  controller.selectedLocation.value.longitude !=
                                      0.0)
                              ? 2
                              : 0,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.check_circle, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          "تأكيد الموقع",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  )),
            ),
          ),
        );
      },
    );
  }
}
