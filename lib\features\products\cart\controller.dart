import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shop_app/features/products/cart/models/cart.dart';

import '../../../core/services/rest_api/rest_api.dart';

class CartPageController extends GetxController {
  final box = GetStorage();

  RxList<CartModel> cartItems = <CartModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadCart();
  }

  // ✅ تحميل البيانات وتحديث cartItems فقط
  void loadCart() {
    final data = box.read<List>('cart_items') ?? [];
    cartItems.value = data
        .map((e) => CartModel.fromJson(Map<String, dynamic>.from(e)))
        .toList();
  }

  // ✅ الإضافة مباشرة إلى cartItems
  void addToCart(CartModel product) {
    cartItems.add(product);
    saveCart();
  }

  void removeFromCart(int productId) {
    cartItems.removeWhere((p) => p.id == productId);
    saveCart();
  }

  void clearCart() {
    cartItems.clear();
    box.remove('cart_items');
  }

  // ✅ الحفظ إلى GetStorage
  void saveCart() {
    box.write('cart_items', cartItems.map((e) => e.toJson()).toList());
  }


  /************ */
  fetch() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.product(id),
        fromJson: ProductDetails.fromJson,
      ),
    );

    if (response.success) {
      product.value = response.data[0];
      price.value = product.value!.price;
    } else {
      product.error = response.message;
    }
  }

}
