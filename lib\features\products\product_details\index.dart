import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/demo/media.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';
import 'package:shop_app/gen/assets.gen.dart';
import 'controller.dart';
import 'usecases/options/index.dart';

class ProductDetailsPage extends StatelessWidget {
  ProductDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProductDetailsPageController());
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        toolbarHeight: MediaQuery.of(context).size.height * .3,
        flexibleSpace: Container(
          decoration: BoxDecoration(
              color: Color(0xFFF2F3F2),
              borderRadius: BorderRadius.vertical(bottom: Radius.circular(30))),
          child: Stack(
            fit: StackFit.expand,
            children: [
              CarouselSlider(
                options: CarouselOptions(
                  viewportFraction: 1, // ¾ من عرض الشاشة
                  enlargeCenterPage: false,
                  autoPlay: true,
                  aspectRatio: 16 / 6,
                  onPageChanged: (index, _) => controller.currentAd = index,
                ),
                items: List.generate(
                  4,
                  (index) => Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30),
                    child: AppImage(
                      path: DemoMedia.getAppRandomImage,
                      type: ImageType.CachedNetwork,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 10,
                left: 0,
                right: 0,
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      4,
                      (index) => Obx(() => AnimatedContainer(
                            duration: 300.milliseconds,
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            height: controller.currentAd == index ? 6 : 6,
                            width: controller.currentAd == index ? 15 : 6,
                            decoration: BoxDecoration(
                              shape: controller.currentAd == index
                                  ? BoxShape.rectangle
                                  : BoxShape.circle,
                              color: controller.currentAd == index
                                  ? StyleRepo.green
                                  : StyleRepo.lightGrey,
                            ),
                          )),
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 30,
                left: 10,
                right: 10,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // زر الرجوع
                    IconButton(
                      icon: Icon(Icons.arrow_back, color: Colors.black),
                      onPressed: () {
                        Get.back();
                      },
                    ),
                    Assets.icons.share.svg()
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      body: // باقي الصفحة

          ObsVariableBuilder(
        obs: controller.product,
        builder: (context, product) {
          return ListView(
            children: [
              SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          product.name,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Assets.icons.love.svg(color: StyleRepo.darkGrey)
                      ],
                    ),
                    SizedBox(height: 16),
                    Text(
                      product.description,
                      style: TextStyle(
                        color: StyleRepo.darkGrey,
                      ),
                    ),
                    SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        IconButton(
                          onPressed: () {
                            if (controller.quantity != 0) {
                              controller.quantity--;
                            }
                          },
                          icon: Assets.icons.minus.svg(width: 20),
                        ),
                        Container(
                          width: 45,
                          height: 45,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(
                              Radius.circular(17),
                            ),
                            border: Border.all(
                              color: StyleRepo.lightGrey,
                              width: 2.0,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              controller.quantity.toString(),
                              style: TextStyle(
                                  color: StyleRepo.black, fontSize: 20),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            controller.quantity++;
                          },
                          icon: Assets.icons.add.svg(
                              colorFilter: ColorFilter.mode(
                                  Theme.of(context).primaryColor,
                                  BlendMode.srcIn)),
                        ),
                        Spacer(),
                        Obx(
                          () => Text(
                            "${controller.price.value * controller.quantity.value} \$",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                  ],
                ),
              ),
              ListView.separated(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: 16),
                itemCount: product.options.length,
                separatorBuilder: (_, __) => SizedBox(height: 12),
                itemBuilder: (context, index) => OptionsListWidget(
                  optionsList: product.options[index],
                  onChanged: (option) => controller.price.value = option.price,
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Obx(
                  () => ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      minimumSize: Size(120, 60),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(19),
                      ),
                      backgroundColor: controller.quantity == 0
                          ? StyleRepo.darkGrey // لون الزر عند التعطيل
                          : Theme.of(context).primaryColor,
                    ),
                    onPressed:
                        controller.quantity == 0 ? () {} : controller.confirm,
                    child: Text(
                      tr(LocaleKeys.Add_To_Cart),
                      style: TextStyle(fontSize: 20, color: StyleRepo.white),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
