import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:shop_app/core/localization/strings.dart';

import '../../../core/services/rest_api/rest_api.dart';

class CompleteInformationPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController email, Full_name;

  Rx<String> profileImage = "".obs; // صورة الملف الشخصي
  Rx<String> idImage = "".obs;

  var isImageErrorVisible = false.obs;

  pickImage(String imageType) async {
    final picker = ImagePicker();
    XFile? picked = await picker.pickImage(source: ImageSource.gallery);
    if (picked == null) return; // إذا لم يتم اختيار صورة، لا تفعل شيئًا
    if (imageType == 'profile') {
      profileImage.value = picked.path; // تخزين صورة الملف الشخصي
    } else if (imageType == 'id') {
      idImage.value = picked.path; // تخزين صورة الهوية
    }
  }

  @override
  onInit() {
    email = TextEditingController();
    Full_name = TextEditingController();

    super.onInit();
  }

  @override
  onClose() {
    email.dispose();
    Full_name.dispose();
    super.onClose();
  }

  void confirm() async {
    if (formKey.currentState!.validate()) {
      // البيانات صحيحة
      print(tr(LocaleKeys.VALID));
      // نفذ العملية أو الانتقال
    } else {
      // غير صالحة – سيتم إظهار رسائل الخطأ تلقائيًا
      print(tr(LocaleKeys.INVALID));
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.register,
        method: RequestMethod.Post,
        params: {"success": true},
        body: FormData.fromMap(
          {
            "email": email.text,
          },
        ),
      ),
    );
    if (response.success) {
    } else {}
  }
}
