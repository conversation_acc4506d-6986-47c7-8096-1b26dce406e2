import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/style/repo.dart';

import '../../models/product_details.dart';
import 'controller.dart';
import 'widgets/option.dart';

class OptionsListWidget extends StatelessWidget {
  final ProductOptionsList optionsList;
  final void Function(ProductOption option) onChanged;
  const OptionsListWidget(
      {super.key, required this.optionsList, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    Get.put(OptionsListController(defaultId: optionsList.optionDefault),
        tag: "${optionsList.options.first.id}");
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: StyleRepo.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: StyleRepo.darkGrey,
            blurRadius: 5,
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Size",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 12),
          ListView.separated(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: optionsList.options.length,
            separatorBuilder: (_, __) => SizedBox(height: 8),
            itemBuilder: (context, index) => OptionWidget(
              option: optionsList.options[index],
              optionsListId: optionsList.options.first.id,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }
}
