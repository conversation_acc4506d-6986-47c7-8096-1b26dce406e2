import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:shop_app/core/localization/localization.dart';
import 'package:shop_app/core/routes/routes.dart';

class RegisterPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController phoneNumber, password;
  var passwordRegex = RegExp(r'^.{8,}$');
  @override
  onInit() {
    phoneNumber = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    phoneNumber.dispose();
    password.dispose();

    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }
    Get.toNamed(Pages.verify.value);
  }

  var isArabic = false.obs;

  void toggleLanguage() {
    isArabic.value = !isArabic.value;

    if (isArabic.value) {
      Get.context?.setLocale(AppLocalization.ar.locale);
      Get.updateLocale(AppLocalization.ar.locale);
    } else {
      Get.context?.setLocale(AppLocalization.en.locale);
      Get.updateLocale(AppLocalization.en.locale);
    }
  }
}
