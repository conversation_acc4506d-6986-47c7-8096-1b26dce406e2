import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/demo/media.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';
import 'package:shop_app/features/Account/controller.dart';
import 'package:shop_app/features/Account/widgets/ListTile.dart';
import 'package:shop_app/features/Account/widgets/menucard.dart';
import 'package:shop_app/gen/assets.gen.dart';

class AccountPage extends StatelessWidget {
  AccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(AccountPageController);
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            StyleRepo.gray,
            StyleRepo.white,
          ],
        ),
      ),
      child: ListView(
        children: [
          const SizedBox(height: 20),
          // Enhanced Profile Header
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [StyleRepo.blue, StyleRepo.green],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: StyleRepo.green.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(35),
                        border: Border.all(color: StyleRepo.white, width: 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 10,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: AppImage(
                        width: 70,
                        height: 70,
                        path: DemoMedia.getAppRandomImage,
                        type: ImageType.CachedNetwork,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(35),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Afsar Hossen',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '<EMAIL>',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Text(
                              '⭐ Premium Member',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: () {
                          Get.toNamed(Pages.editprofile.value);
                        },
                        icon: Icon(Icons.edit, color: Colors.white, size: 20),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // Stats Row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    buildStatItem(
                        '12', tr(LocaleKeys.Orders), Icons.shopping_bag),
                    buildStatItem(
                        '5', tr(LocaleKeys.favourite), Icons.favorite),
                    buildStatItem('3', tr(LocaleKeys.Rating), Icons.star),
                  ],
                ),
              ],
            ),
          ),

          // Language Switch Section
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.language,
                    color: Colors.blue.shade600,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tr(LocaleKeys.language),
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey.shade800,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        context.locale.languageCode == 'ar'
                            ? tr(LocaleKeys.arabic)
                            : tr(LocaleKeys.english),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      GestureDetector(
                        onTap: () => changeLanguage(context, 'en'),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: context.locale.languageCode == 'en'
                                ? Colors.blue.shade600
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            'EN',
                            style: TextStyle(
                              color: context.locale.languageCode == 'en'
                                  ? Colors.white
                                  : Colors.grey.shade600,
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () => changeLanguage(context, 'ar'),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: context.locale.languageCode == 'ar'
                                ? Colors.blue.shade600
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            'ع',
                            style: TextStyle(
                              color: context.locale.languageCode == 'ar'
                                  ? Colors.white
                                  : Colors.grey.shade600,
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Menu Cards Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Column(
              children: [
                // First Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.orders.svg(),
                        tr(LocaleKeys.Orders),
                        Colors.blue.shade400,
                        () {},
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.myDetails.svg(),
                        tr(LocaleKeys.My_Details),
                        Colors.purple.shade400,
                        () {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Second Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.deliveryAddress.svg(),
                        tr(LocaleKeys.Delivery_Address),
                        Colors.orange.shade400,
                        () => Get.toNamed(Pages.location_picker.value),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.paymentMethods.svg(),
                        tr(LocaleKeys.Payment_Methods),
                        Colors.teal.shade400,
                        () {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Third Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.promoCard.svg(),
                        tr(LocaleKeys.Promo_Code),
                        Colors.pink.shade400,
                        () {},
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.notification.svg(),
                        tr(LocaleKeys.Notifications),
                        Colors.indigo.shade400,
                        () {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Fourth Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.help.svg(),
                        tr(LocaleKeys.Help),
                        Colors.amber.shade400,
                        () {},
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.about.svg(),
                        tr(LocaleKeys.About),
                        Colors.cyan.shade400,
                        () => Get.toNamed(Pages.about_us.value),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 30),

          // Logout Button
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade400,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 5,
              ),
              onPressed: () {},
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icons.logOut.svg(
                    colorFilter:
                        ColorFilter.mode(Colors.white, BlendMode.srcIn),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    tr(LocaleKeys.Log_Out),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // تغيير اللغة
  void changeLanguage(BuildContext context, String languageCode) async {
    Locale newLocale = Locale(languageCode);
    await context.setLocale(newLocale);
    Get.updateLocale(newLocale);

    Get.snackbar(
      tr(LocaleKeys.success),
      tr(LocaleKeys.language_changed),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: Duration(seconds: 2),
    );
  }
}
