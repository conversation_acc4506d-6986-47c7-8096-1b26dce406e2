import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/demo/media.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';
import 'package:shop_app/features/Account/controller.dart';
import 'package:shop_app/features/Account/widgets/ListTile.dart';
import 'package:shop_app/gen/assets.gen.dart';

class AccountPage extends StatelessWidget {
  AccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AccountPageController);
    return ListView(
      children: [
        const SizedBox(height: 20),
        // Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              AppImage(
                width: 65,
                height: 65,
                path: DemoMedia.getAppRandomImage,
                type: ImageType.CachedNetwork,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(27),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Afsar Hossen',
                        style: Theme.of(context).textTheme.titleLarge),
                    Text('<EMAIL>',
                        style: Theme.of(context).textTheme.labelMedium),
                  ],
                ),
              ),
              InkResponse(
                  onTap: () {
                    Get.toNamed(Pages.editprofile.value);
                  },
                  child: Icon(Icons.edit, size: 18, color: Colors.green)),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Divider(
          color: StyleRepo.gray,
        ),
        buildTile(
          context,
          Assets.icons.orders.svg(),
          tr(LocaleKeys.Orders),
        ),
        Divider(
          color: StyleRepo.gray,
        ),
        buildTile(
          context,
          Assets.icons.myDetails.svg(),
          tr(LocaleKeys.My_Details),
        ),
        Divider(
          color: StyleRepo.gray,
        ),
        buildTile(
          context,
          Assets.icons.deliveryAddress.svg(),
          tr(LocaleKeys.Delivery_Address),
        ),
        Divider(
          color: StyleRepo.gray,
        ),
        buildTile(
          context,
          Assets.icons.paymentMethods.svg(),
          tr(LocaleKeys.Payment_Methods),
        ),
        Divider(
          color: StyleRepo.gray,
        ),
        buildTile(
          context,
          Assets.icons.promoCard.svg(),
          tr(LocaleKeys.Promo_Code),
        ),
        Divider(
          color: StyleRepo.gray,
        ),
        buildTile(
          context,
          Assets.icons.notification.svg(),
          tr(LocaleKeys.Notifications),
        ),
        Divider(
          color: StyleRepo.gray,
        ),
        buildTile(
          context,
          Assets.icons.help.svg(),
          tr(LocaleKeys.Help),
        ),
        Divider(
          color: StyleRepo.gray,
        ),
        buildTile(
          context,
          Assets.icons.about.svg(),
          tr(LocaleKeys.About),
          onTap: () => Get.toNamed(Pages.about_us.value),
        ),
        const SizedBox(height: 20),

        Center(
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: StyleRepo.meduimGrey,
              foregroundColor: StyleRepo.green,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(19),
              ),
              fixedSize: Size(365, 67),
            ),
            onPressed: () {},
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Assets.icons.logOut.svg(),
                const SizedBox(width: 100),
                Text(
                  tr(LocaleKeys.Log_Out),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
