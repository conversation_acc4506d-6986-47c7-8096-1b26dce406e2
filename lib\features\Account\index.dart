import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/demo/media.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';
import 'package:shop_app/features/Account/controller.dart';
import 'package:shop_app/features/Account/widgets/ListTile.dart';
import 'package:shop_app/features/Account/widgets/menucard.dart';
import 'package:shop_app/gen/assets.gen.dart';

class AccountPage extends StatefulWidget {
  AccountPage({super.key});

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage>
    with TickerProviderStateMixin {
  // متغير لتتبع موضع السحب
  double _dragPosition = 0.0;
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Get.put(AccountPageController);
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            StyleRepo.gray,
            StyleRepo.white,
          ],
        ),
      ),
      child: ListView(
        children: [
          const SizedBox(height: 20),
          // Enhanced Profile Header
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [StyleRepo.blue, StyleRepo.green],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: StyleRepo.green.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(35),
                        border: Border.all(color: StyleRepo.white, width: 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 10,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: AppImage(
                        width: 70,
                        height: 70,
                        path: DemoMedia.getAppRandomImage,
                        type: ImageType.CachedNetwork,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(35),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Afsar Hossen',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '<EMAIL>',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Text(
                              '⭐ Premium Member',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: () {
                          Get.toNamed(Pages.editprofile.value);
                        },
                        icon: Icon(Icons.edit, color: Colors.white, size: 20),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // Stats Row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    buildStatItem(
                        '12', tr(LocaleKeys.Orders), Icons.shopping_bag),
                    buildStatItem(
                        '5', tr(LocaleKeys.favourite), Icons.favorite),
                    buildStatItem('3', tr(LocaleKeys.Rating), Icons.star),
                  ],
                ),
              ],
            ),
          ),

          // Language Switch Section
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF0866FF), // Meta Blue
                  Color(0xFF1877F2), // Facebook Blue
                  Color(0xFF42A5F5), // Light Blue
                  Color(0xFF29B6F6), // Sky Blue
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Color(0xFF0866FF).withValues(alpha: 0.4),
                  blurRadius: 15,
                  offset: const Offset(0, 6),
                ),
                BoxShadow(
                  color: Color(0xFF1877F2).withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.language,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tr(LocaleKeys.language),
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        context.locale.languageCode == 'ar'
                            ? tr(LocaleKeys.arabic)
                            : tr(LocaleKeys.english),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () => _toggleLanguage(context),
                  onPanStart: (details) => _handlePanStart(),
                  onPanUpdate: (details) => _handlePanUpdate(details),
                  onPanEnd: (details) => _handlePanEnd(details, context),
                  child: Container(
                    width: 100,
                    height: 42,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withValues(alpha: 0.9),
                          Colors.white.withValues(alpha: 0.8),
                          Color(0xFFF0F4FF).withValues(alpha: 0.9),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.2),
                          blurRadius: 4,
                          offset: const Offset(0, -1),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // خلفية النصوص
                        Positioned.fill(
                          child: Row(
                            children: [
                              Expanded(
                                child: Center(
                                  child: AnimatedOpacity(
                                    opacity: _getTextOpacity(true),
                                    duration: const Duration(milliseconds: 150),
                                    child: Text(
                                      'EN',
                                      style: TextStyle(
                                        color: Color(0xFF0866FF)
                                            .withValues(alpha: 0.7),
                                        fontWeight: FontWeight.w600,
                                        fontSize: 11,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Center(
                                  child: AnimatedOpacity(
                                    opacity: _getTextOpacity(false),
                                    duration: const Duration(milliseconds: 150),
                                    child: Text(
                                      'ع',
                                      style: TextStyle(
                                        color: Color(0xFF0866FF)
                                            .withValues(alpha: 0.7),
                                        fontWeight: FontWeight.w600,
                                        fontSize: 11,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // الكرة المتحركة
                        Positioned(
                          left: _getSliderPosition(),
                          top: 4,
                          child: AnimatedContainer(
                            duration: _isDragging
                                ? Duration.zero
                                : const Duration(milliseconds: 200),
                            curve: Curves.easeInOut,
                            width: 34,
                            height: 34,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Color(0xFF0866FF), // Meta Blue
                                  Color(0xFF1877F2), // Facebook Blue
                                  Color(0xFF42A5F5), // Light Blue
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                              borderRadius: BorderRadius.circular(17),
                              boxShadow: [
                                BoxShadow(
                                  color:
                                      Color(0xFF0866FF).withValues(alpha: 0.4),
                                  blurRadius: _isDragging ? 10 : 8,
                                  offset: const Offset(0, 3),
                                ),
                                BoxShadow(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  blurRadius: 2,
                                  offset: const Offset(0, -1),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                _getCurrentLanguageText(),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Menu Cards Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Column(
              children: [
                // First Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.orders.svg(),
                        tr(LocaleKeys.Orders),
                        Colors.blue.shade400,
                        () {},
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.myDetails.svg(),
                        tr(LocaleKeys.My_Details),
                        Colors.purple.shade400,
                        () {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Second Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.deliveryAddress.svg(),
                        tr(LocaleKeys.Delivery_Address),
                        Colors.orange.shade400,
                        () => Get.toNamed(Pages.location_picker.value),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.paymentMethods.svg(),
                        tr(LocaleKeys.Payment_Methods),
                        Colors.teal.shade400,
                        () {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Third Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.promoCard.svg(),
                        tr(LocaleKeys.Promo_Code),
                        Colors.pink.shade400,
                        () {},
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.notification.svg(),
                        tr(LocaleKeys.Notifications),
                        Colors.indigo.shade400,
                        () {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Fourth Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.help.svg(),
                        tr(LocaleKeys.Help),
                        Colors.amber.shade400,
                        () {},
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.about.svg(),
                        tr(LocaleKeys.About),
                        Colors.cyan.shade400,
                        () => Get.toNamed(Pages.about_us.value),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 30),

          // Logout Button
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade400,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 5,
              ),
              onPressed: () {},
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icons.logOut.svg(
                    colorFilter:
                        ColorFilter.mode(Colors.white, BlendMode.srcIn),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    tr(LocaleKeys.Log_Out),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // تغيير اللغة
  void changeLanguage(BuildContext context, String languageCode) async {
    Locale newLocale = Locale(languageCode);
    await context.setLocale(newLocale);
    Get.updateLocale(newLocale);

    Get.snackbar(
      tr(LocaleKeys.success),
      tr(LocaleKeys.language_changed),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: Duration(seconds: 2),
    );
  }

  // تبديل اللغة بالنقر
  void _toggleLanguage(BuildContext context) {
    String newLanguage = context.locale.languageCode == 'ar' ? 'en' : 'ar';
    changeLanguage(context, newLanguage);
  }

  // بداية السحب
  void _handlePanStart() {
    setState(() {
      _isDragging = true;
      _dragPosition = 0.0;
    });
  }

  // التعامل مع تحديث السحب
  void _handlePanUpdate(DragUpdateDetails details) {
    setState(() {
      _dragPosition += details.delta.dx;
      // تحديد الحد الأدنى والأقصى للسحب (توسيع المجال)
      _dragPosition = _dragPosition.clamp(-62.0, 62.0);
    });
  }

  // التعامل مع انتهاء السحب
  void _handlePanEnd(DragEndDetails details, BuildContext context) {
    setState(() {
      _isDragging = false;
    });

    // تحديد اللغة بناءً على موضع السحب
    bool shouldChangeToArabic = _dragPosition > 15;
    bool shouldChangeToEnglish = _dragPosition < -15;

    if (shouldChangeToArabic && context.locale.languageCode == 'en') {
      changeLanguage(context, 'ar');
    } else if (shouldChangeToEnglish && context.locale.languageCode == 'ar') {
      changeLanguage(context, 'en');
    }

    // إعادة تعيين موضع السحب
    _dragPosition = 0.0;
  }

  // حساب موضع الكرة المتحركة
  double _getSliderPosition() {
    if (_isDragging) {
      // أثناء السحب، تحريك الكرة مع السحب
      double basePosition = context.locale.languageCode == 'ar' ? 56.0 : 4.0;
      return (basePosition + _dragPosition).clamp(4.0, 56.0);
    } else {
      // عند عدم السحب، موضع ثابت حسب اللغة
      return context.locale.languageCode == 'ar' ? 56.0 : 4.0;
    }
  }

  // حساب شفافية النص
  double _getTextOpacity(bool isEnglish) {
    if (!_isDragging) {
      return 1.0;
    }

    double sliderPosition = _getSliderPosition();

    if (isEnglish) {
      // النص الإنجليزي يصبح أكثر وضوحاً كلما اقتربت الكرة من اليسار
      return (1.0 - (sliderPosition - 4.0) / 40.0).clamp(0.3, 1.0);
    } else {
      // النص العربي يصبح أكثر وضوحاً كلما اقتربت الكرة من اليمين
      return ((sliderPosition - 4.0) / 40.0).clamp(0.3, 1.0);
    }
  }

  // الحصول على نص اللغة الحالية
  String _getCurrentLanguageText() {
    if (_isDragging) {
      double sliderPosition = _getSliderPosition();
      // تغيير النص بناءً على موضع الكرة أثناء السحب
      return sliderPosition > 24 ? 'ع' : 'EN';
    } else {
      return context.locale.languageCode == 'ar' ? 'ع' : 'EN';
    }
  }
}
