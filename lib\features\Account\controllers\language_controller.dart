import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/style/repo.dart';
import '../../../generated/locale_keys.g.dart';

class LanguageController extends GetxController {
  // متغيرات reactive
  final RxBool isDragging = false.obs;
  final RxDouble dragPosition = 0.0.obs;

  // بداية السحب
  void handlePanStart() {
    isDragging.value = true;
    dragPosition.value = 0.0;
  }

  // التعامل مع تحديث السحب
  void handlePanUpdate(DragUpdateDetails details) {
    dragPosition.value += details.delta.dx;
    // تحديد الحد الأدنى والأقصى للسحب (توسيع المجال)
    dragPosition.value = dragPosition.value.clamp(-62.0, 62.0);
  }

  // التعامل مع انتهاء السحب
  void handlePanEnd(DragEndDetails details, BuildContext context) {
    isDragging.value = false;

    // تحديد اللغة بناءً على موضع السحب
    bool shouldChangeToArabic = dragPosition.value > 15;
    bool shouldChangeToEnglish = dragPosition.value < -15;

    if (shouldChangeToArabic && context.locale.languageCode == 'en') {
      changeLanguage(context, 'ar');
    } else if (shouldChangeToEnglish && context.locale.languageCode == 'ar') {
      changeLanguage(context, 'en');
    }

    // إعادة تعيين موضع السحب
    dragPosition.value = 0.0;
  }

  // حساب موضع الكرة المتحركة
  double getSliderPosition(BuildContext context) {
    if (isDragging.value) {
      // أثناء السحب، تحريك الكرة مع السحب
      double basePosition = context.locale.languageCode == 'ar' ? 56.0 : 4.0;
      return (basePosition + dragPosition.value).clamp(4.0, 56.0);
    } else {
      // عند عدم السحب، موضع ثابت حسب اللغة
      return context.locale.languageCode == 'ar' ? 56.0 : 4.0;
    }
  }

  // الحصول على النص المعاكس للغة الحالية في الخلفية
  String getBackgroundText(bool isLeftSide, BuildContext context) {
    if (isLeftSide) {
      // الجانب الأيسر - دائماً EN
      return 'EN';
    } else {
      // الجانب الأيمن - دائماً ع
      return 'ع';
    }
  }

  // حساب شفافية النص في الخلفية
  double getBackgroundTextOpacity(bool isEnglish, BuildContext context) {
    if (!isDragging.value) {
      // عندما لا يكون هناك سحب، إخفاء النص الذي تغطيه الكرة
      if (isEnglish) {
        return context.locale.languageCode == 'en' ? 0.3 : 1.0;
      } else {
        return context.locale.languageCode == 'ar' ? 0.3 : 1.0;
      }
    }

    double sliderPosition = getSliderPosition(context);

    if (isEnglish) {
      // النص الإنجليزي يصبح أكثر وضوحاً كلما ابتعدت الكرة عنه
      return (1.0 - (sliderPosition - 4.0) / 52.0).clamp(0.3, 1.0);
    } else {
      // النص العربي يصبح أكثر وضوحاً كلما ابتعدت الكرة عنه
      return ((sliderPosition - 4.0) / 52.0).clamp(0.3, 1.0);
    }
  }

  // الحصول على نص اللغة الحالية في الكرة
  String getCurrentLanguageText(BuildContext context) {
    if (isDragging.value) {
      double sliderPosition = getSliderPosition(context);
      // تغيير النص بناءً على موضع الكرة أثناء السحب (نقطة المنتصف الجديدة)
      return sliderPosition > 30 ? 'ع' : 'EN';
    } else {
      // عرض النص المعاكس للغة الحالية (النص الذي سيتم التبديل إليه)
      return context.locale.languageCode == 'ar' ? 'ع' : 'EN';
    }
  }

  // تغيير اللغة
  void changeLanguage(BuildContext context, String languageCode) async {
    Locale newLocale = Locale(languageCode);
    await context.setLocale(newLocale);
    Get.updateLocale(newLocale);

    Get.snackbar(
      'نجح',
      'تم تغيير اللغة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: StyleRepo.purple,
      colorText: StyleRepo.white,
      duration: Duration(seconds: 2),
    );
  }

  // تبديل اللغة بالنقر
  void toggleLanguage(BuildContext context) {
    String newLanguage = context.locale.languageCode == 'ar' ? 'en' : 'ar';
    changeLanguage(context, newLanguage);
  }
}
