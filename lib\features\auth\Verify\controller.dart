import 'dart:async';
import 'package:get/get.dart';

class VerifyPageController extends GetxController {
  RxInt secondsLeft = 60.obs;
  RxBool isResendActive = false.obs;
  Timer? _timer;

  void startTimer() {
    secondsLeft.value = 60;
    isResendActive.value = false;

    _timer?.cancel(); // إلغاء المؤقت السابق إن وجد
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (secondsLeft.value == 0) {
        isResendActive.value = true;
        timer.cancel();
      } else {
        secondsLeft.value--;
      }
    });
  }

  String get formattedTime {
    final minutes = (secondsLeft.value ~/ 60).toString().padLeft(2, '0');
    final secs = (secondsLeft.value % 60).toString().padLeft(2, '0');
    return '$minutes : $secs';
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }

  @override
  onInit() {
    startTimer();
    super.onInit();
  }
}
