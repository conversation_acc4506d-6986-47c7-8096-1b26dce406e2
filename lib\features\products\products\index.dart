import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/constants/controllers_tags.dart';
import 'package:shop_app/core/models/product/product.dart';
import 'package:shop_app/core/services/pagination/options/list_view.dart';

import 'controller.dart';

class ProductsPage extends StatelessWidget {
  const ProductsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProductsPageController());
    return Scaffold(
      appBar: AppBar(),
      body: ListViewPagination.separated(
        //
        tag: ControllersTags.products_pager,
        fetchApi: controller.fetchData,
        fromJson: MainProduct.fromJson,
        //
        initialLoading: Placeholder(),
        errorWidget: (error) => Text(error),
        onControllerInit: (pagerController) =>
            controller.pagerController = pagerController,

        separatorBuilder: (_, __) => SizedBox(height: 16),
        itemBuilder: (context, index, product) {
          return SizedBox(
            height: 300,
            child: Center(
              child: Text(product.name),
            ),
          );
        },
      ),
    );
  }
}
