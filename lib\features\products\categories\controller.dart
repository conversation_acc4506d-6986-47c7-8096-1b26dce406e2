import 'package:get/get.dart';
import 'package:shop_app/core/models/category.dart';
import 'package:shop_app/core/services/rest_api/api_service.dart';
import 'package:shop_app/core/services/rest_api/constants/end_points.dart';
import 'package:shop_app/core/services/rest_api/models/request.dart';
import 'package:shop_app/core/services/rest_api/models/response_model.dart';
import 'package:shop_app/core/services/state_management/obs.dart';

class CategoriesPageController extends GetxController {
  ObsList<CategoryModel> categories = ObsList([]);

  fetchCategories() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.categories,
        fromJson: CategoryModel.fromJson,
        copyHeader: {"Accept-Language": "en"},
      ),
    );

    if (response.success) {
      categories.value = response.data;
    } else {
      categories.error = response.message;
    }
  }
  //!SECTION

  @override
  void onInit() {
    fetchCategories();
    super.onInit();
  }
}
