import 'dart:developer';
import 'package:get/get.dart';
import 'package:shop_app/core/services/rest_api/rest_api.dart';
import 'package:shop_app/core/services/state_management/obs.dart';
import 'package:shop_app/features/products/cart/controller.dart';
import 'package:shop_app/features/products/cart/models/cart.dart';
import 'package:shop_app/features/products/product_details/models/product_details.dart';

class ProductDetailsPageController extends GetxController {
  final controller = Get.find<CartPageController>();

  late final int id;
  RxDouble price = 0.0.obs;
  RxInt quantity = 0.obs;

  final Rx<int> _currentAd = 0.obs;
  int get currentAd => _currentAd.value;
  set currentAd(int value) => _currentAd.value = value;
  ObsVar<ProductDetails> product = ObsVar(null);

  fetch() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.product(id),
        fromJson: ProductDetails.fromJson,
      ),
    );

    if (response.success) {
      product.value = response.data[0];
      price.value = product.value!.price;
    } else {
      product.error = response.message;
    }
  }

  confirm() async {
    CartModel newCart = CartModel(
        id: product.value!.id,
        name: product.value!.name,
        Price: product.value!.price,
        quntity: quantity.toInt());
    controller.addToCart(newCart);

    // ResponseModel responsee = await APIService.instance.request(
    //   Request(
    //     endPoint: EndPoints.storeproduct(id),
    //     method: RequestMethod.Post,
    //     body: {
    //       // "name": product.value!.name,
    //       // "quantity": quantity.toInt(),
    //       newCart.toJson(), // ✅ هنا التحويل إلى Map
    //     },
    //   ),
    // );
    // if (responsee.success) {
    // } else {}

    Get.snackbar("تمت الإضافة", "${newCart.name} تمت إضافته إلى السلة");
  }

  @override
  onInit() {
    id = Get.arguments;
    log(id.toString());
    fetch();
    super.onInit();
  }
}
