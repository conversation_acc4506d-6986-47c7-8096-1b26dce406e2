import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlong;
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

class MapLocationPickerController extends GetxController {
  MapController? mapController;
  
  // المتغيرات التفاعلية
  Rx<latlong.LatLng> selectedLocation = latlong.LatLng(24.7136, 46.6753).obs; // الرياض كموقع افتراضي
  RxString selectedAddress = ''.obs;
  RxBool isLoading = false.obs;
  RxDouble currentZoom = 15.0.obs;

  @override
  void onInit() {
    super.onInit();
    mapController = MapController();
    // طلب الصلاحيات عند بدء الصفحة
    _requestLocationPermission();
  }

  // عند النقر على الخريطة
  void onMapTappedOSM(latlong.LatLng location) {
    selectedLocation.value = location;
    _getAddressFromCoordinates(location);
  }

  // الحصول على الموقع الحالي
  void getCurrentLocation() async {
    try {
      isLoading.value = true;
      
      // التحقق من الصلاحيات
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        Get.snackbar(
          'خطأ',
          'خدمة الموقع غير مفعلة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          Get.snackbar(
            'خطأ',
            'تم رفض صلاحية الوصول للموقع',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          isLoading.value = false;
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        Get.snackbar(
          'خطأ',
          'صلاحية الموقع مرفوضة نهائياً. يرجى تفعيلها من الإعدادات',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return;
      }

      // الحصول على الموقع الحالي
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      latlong.LatLng currentLocation = latlong.LatLng(position.latitude, position.longitude);
      selectedLocation.value = currentLocation;
      
      // تحريك الكاميرا للموقع الحالي
      if (mapController != null) {
        mapController!.move(currentLocation, 16.0);
        currentZoom.value = 16.0;
      }

      _getAddressFromCoordinates(currentLocation);

    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ في الحصول على الموقع: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // تكبير الخريطة
  void zoomIn() {
    if (mapController != null && currentZoom.value < 18) {
      currentZoom.value += 1;
      mapController!.move(selectedLocation.value, currentZoom.value);
    }
  }

  // تصغير الخريطة
  void zoomOut() {
    if (mapController != null && currentZoom.value > 3) {
      currentZoom.value -= 1;
      mapController!.move(selectedLocation.value, currentZoom.value);
    }
  }

  // تحويل الإحداثيات إلى عنوان
  void _getAddressFromCoordinates(latlong.LatLng location) async {
    try {
      isLoading.value = true;
      
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        
        // تكوين العنوان
        List<String> addressParts = [];
        
        if (place.street != null && place.street!.isNotEmpty) {
          addressParts.add(place.street!);
        }
        if (place.subLocality != null && place.subLocality!.isNotEmpty) {
          addressParts.add(place.subLocality!);
        }
        if (place.locality != null && place.locality!.isNotEmpty) {
          addressParts.add(place.locality!);
        }
        if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
          addressParts.add(place.administrativeArea!);
        }
        if (place.country != null && place.country!.isNotEmpty) {
          addressParts.add(place.country!);
        }

        selectedAddress.value = addressParts.join(', ');
        
        if (selectedAddress.value.isEmpty) {
          selectedAddress.value = 'موقع غير محدد';
        }
      } else {
        selectedAddress.value = 'لم يتم العثور على عنوان';
      }
    } catch (e) {
      selectedAddress.value = 'خطأ في تحديد العنوان';
      print('Error getting address: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // طلب صلاحيات الموقع
  void _requestLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      await Geolocator.requestPermission();
    }
  }

  // تأكيد الموقع والعودة بالنتيجة
  void confirmLocation() {
    Map<String, dynamic> result = {
      'latitude': selectedLocation.value.latitude,
      'longitude': selectedLocation.value.longitude,
      'address': selectedAddress.value,
    };
    
    Get.back(result: result);
    
    Get.snackbar(
      'تم بنجاح',
      'تم حفظ الموقع: ${selectedAddress.value}',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: Duration(seconds: 3),
    );
  }
}
