import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlong;
import 'map_controller.dart';

class MapLocationPickerPage extends StatelessWidget {
  const MapLocationPickerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MapLocationPickerController>(
      init: MapLocationPickerController(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: Text('اختيار الموقع'),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            actions: [
              // زر الموقع الحالي
              IconButton(
                onPressed: controller.getCurrentLocation,
                icon: Icon(Icons.my_location),
                tooltip: 'موقعي الحالي',
              ),
            ],
          ),
          body: Stack(
            children: [
              // خريطة OpenStreetMap
              Obx(() => FlutterMap(
                    options: MapOptions(
                      initialCenter: latlong.LatLng(
                        controller.selectedLocation.value.latitude,
                        controller.selectedLocation.value.longitude,
                      ),
                      initialZoom: 15.0,
                      onTap: (tapPosition, point) {
                        controller.onMapTappedOSM(point);
                      },
                    ),
                    children: [
                      // طبقة الخريطة
                      TileLayer(
                        urlTemplate:
                            'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                        userAgentPackageName: 'com.example.shop_app',
                        maxZoom: 19,
                      ),
                      // طبقة العلامات
                      MarkerLayer(
                        markers: [
                          if (controller.selectedLocation.value.latitude != 0)
                            Marker(
                              point: latlong.LatLng(
                                controller.selectedLocation.value.latitude,
                                controller.selectedLocation.value.longitude,
                              ),
                              width: 40,
                              height: 40,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.green,
                                  shape: BoxShape.circle,
                                  border:
                                      Border.all(color: Colors.white, width: 3),
                                  boxShadow: [
                                    BoxShadow(
                                      color:
                                          Colors.black.withValues(alpha: 0.3),
                                      blurRadius: 6,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.location_on,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  )),

              // معلومات الموقع المحدد
              Positioned(
                top: 16,
                left: 16,
                right: 16,
                child: Obx(
                  () => controller.selectedAddress.value.isNotEmpty
                      ? Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.location_on,
                                      color: Colors.green, size: 20),
                                  const SizedBox(width: 8),
                                  Text(
                                    'الموقع المحدد',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                controller.selectedAddress.value,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ],
                          ),
                        )
                      : Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.info, color: Colors.blue, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'اضغط على الخريطة لتحديد الموقع',
                                  style: TextStyle(
                                    color: Colors.blue.shade700,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                ),
              ),

              // مؤشر التحميل
              Obx(
                () => controller.isLoading.value
                    ? Container(
                        color: Colors.black.withValues(alpha: 0.3),
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CircularProgressIndicator(color: Colors.green),
                                const SizedBox(height: 16),
                                Text('جاري تحديد الموقع...'),
                              ],
                            ),
                          ),
                        ),
                      )
                    : SizedBox.shrink(),
              ),

              // أزرار التحكم في الخريطة
              Positioned(
                right: 16,
                bottom: 100,
                child: Column(
                  children: [
                    // زر التكبير
                    FloatingActionButton.small(
                      heroTag: "zoom_in",
                      onPressed: controller.zoomIn,
                      backgroundColor: Colors.white,
                      child: Icon(Icons.add, color: Colors.green),
                    ),
                    const SizedBox(height: 8),
                    // زر التصغير
                    FloatingActionButton.small(
                      heroTag: "zoom_out",
                      onPressed: controller.zoomOut,
                      backgroundColor: Colors.white,
                      child: Icon(Icons.remove, color: Colors.green),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // زر تأكيد الموقع
          bottomNavigationBar: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: Obx(() => ElevatedButton(
                    onPressed: controller.selectedAddress.value.isNotEmpty
                        ? () => controller.confirmLocation()
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.check_circle, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'تأكيد الموقع',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  )),
            ),
          ),
        );
      },
    );
  }
}
