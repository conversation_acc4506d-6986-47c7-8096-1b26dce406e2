import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:shop_app/core/localization/strings.dart';

class LocationPickerController extends GetxController {
  GoogleMapController? mapController;
  Rx<LatLng> selectedLocation = LatLng(33.5138, 36.2765).obs; // دمشق، سوريا
  // المتغيرات التفاعلية

  RxString selectedAddress = ''.obs;
  RxBool isLoading = false.obs;
  RxList<Marker> markers = <Marker>[].obs;

  @override
  void onInit() {
    super.onInit();
    // طلب الصلاحيات عند بدء الصفحة
    requestLocationPermission();
  }

  // إنشاء الخريطة
  void onMapCreated(GoogleMapController controller) {
    mapController = controller;
  }

  // عند النقر على الخريطة
  void onMapTapped(LatLng location) {
    selectedLocation.value = location;
    updateMarker(location);
    getAddressFromCoordinates(location);
  }

  // الحصول على الموقع الحالي
  void getCurrentLocation() async {
    try {
      isLoading.value = true;

      // التحقق من الصلاحيات
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        Get.snackbar(
          tr(LocaleKeys.error),
          tr(LocaleKeys.location_service_disabled),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          Get.snackbar(
            tr(LocaleKeys.error),
            tr(LocaleKeys.location_permission_denied),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          isLoading.value = false;
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        Get.snackbar(
          tr(LocaleKeys.error),
          tr(LocaleKeys.location_permission_denied_forever),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return;
      }

      // الحصول على الموقع الحالي
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      LatLng currentLocation = LatLng(position.latitude, position.longitude);
      selectedLocation.value = currentLocation;

      // تحريك الكاميرا للموقع الحالي
      if (mapController != null) {
        await mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(currentLocation, 16.0),
        );
      }

      updateMarker(currentLocation);
      getAddressFromCoordinates(currentLocation);
    } catch (e) {
      Get.snackbar(
        tr(LocaleKeys.error),
        '${tr(LocaleKeys.location_error)}: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // تحديث العلامة على الخريطة
  void updateMarker(LatLng location) {
    markers.clear();
    markers.add(
      Marker(
        markerId: MarkerId('selected_location'),
        position: location,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        infoWindow: InfoWindow(
          title: tr(LocaleKeys.marker_selected_location),
          snippet: tr(LocaleKeys.marker_tap_to_confirm),
        ),
      ),
    );
  }

  // تحويل الإحداثيات إلى عنوان
  void getAddressFromCoordinates(LatLng location) async {
    try {
      isLoading.value = true;

      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];

        // تكوين العنوان
        List<String> addressParts = [];

        if (place.street != null && place.street!.isNotEmpty) {
          addressParts.add(place.street!);
        }
        if (place.subLocality != null && place.subLocality!.isNotEmpty) {
          addressParts.add(place.subLocality!);
        }
        if (place.locality != null && place.locality!.isNotEmpty) {
          addressParts.add(place.locality!);
        }
        if (place.administrativeArea != null &&
            place.administrativeArea!.isNotEmpty) {
          addressParts.add(place.administrativeArea!);
        }
        if (place.country != null && place.country!.isNotEmpty) {
          addressParts.add(place.country!);
        }

        if (addressParts.isNotEmpty) {
          selectedAddress.value = addressParts.join(', ');
        } else {
          // إذا لم نحصل على عنوان، نعرض الإحداثيات
          selectedAddress.value =
              'موقع محدد: ${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)}';
        }
      } else {
        // إذا لم نجد أي عنوان، نعرض الإحداثيات
        selectedAddress.value =
            'موقع محدد: ${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)}';
      }
    } catch (e) {
      // في حالة الخطأ، نعرض الإحداثيات
      selectedAddress.value =
          'موقع محدد: ${location.latitude.toStringAsFixed(4)}, ${location.longitude.toStringAsFixed(4)}';
      print('Error getting address: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // طلب صلاحيات الموقع
  void requestLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      await Geolocator.requestPermission();
    }
  }

  // تأكيد الموقع والعودة بالنتيجة
  void confirmLocation() {
    Map<String, dynamic> result = {
      'latitude': selectedLocation.value.latitude,
      'longitude': selectedLocation.value.longitude,
      'address': selectedAddress.value,
    };

    Get.back(result: result);

    Get.snackbar(
      tr(LocaleKeys.success),
      '${tr(LocaleKeys.location_saved)}: ${selectedAddress.value}',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: Duration(seconds: 3),
    );
  }
}
