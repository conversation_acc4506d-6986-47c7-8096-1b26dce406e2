// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/features/EditAccount/index.dart';
import 'package:shop_app/features/auth/Verify/index.dart';
import 'package:shop_app/features/auth/complete_information/index.dart';
import 'package:shop_app/features/auth/register/index.dart';
import 'package:shop_app/features/main/index.dart';
import 'package:shop_app/features/products/cart/widgets/OrderComplete.dart';
import 'package:shop_app/features/restaurant/restaurant_in/index.dart';
import 'package:shop_app/features/restaurant/restaurant_out/index.dart';

import '../../features/auth/login/index.dart';
import '../../features/products/product_details/index.dart';
import '../../features/products/products/index.dart';
import '../../features/splash/index.dart';

class AppRouting {
  static GetPage unknownRoute =
      GetPage(name: "/unknown", page: () => SizedBox());

  static GetPage initialRoute = GetPage(
    name: "/",
    page: () => SplashScreen(),
  );

  static List<GetPage> routes = [
    initialRoute,
    ...Pages.values.map((e) => e.page),
  ];
}

enum Pages {
  //Auth
  login,
  register,
  complete,
  verify,
  //
  home,
  product_details,
  products,
  RestaurantOut,
  RestaurantIn,
  OrderComplete,
  editprofile
  //
  ;

  String get value => '/$name';

  GetPage get page => switch (this) {
        login => GetPage(
            name: value,
            page: () => LoginPage(),
          ),
        complete => GetPage(
            name: value,
            page: () => CompleteInformationPage(),
          ),
        verify => GetPage(
            name: value,
            page: () => VerifyPage(),
          ),
        register => GetPage(
            name: value,
            page: () => RegisterPage(),
          ),
        home => GetPage(
            name: value,
            page: () => MainPage(),
          ),
        product_details => GetPage(
            name: value,
            page: () => ProductDetailsPage(),
          ),
        products => GetPage(
            name: value,
            page: () => ProductsPage(),
          ),
        RestaurantOut => GetPage(
            name: value,
            page: () => RestaurantOutPage(),
          ),
        RestaurantIn => GetPage(
            name: value,
            page: () => RestaurantInPage(),
          ),
        OrderComplete => GetPage(
            name: value,
            page: () => OrderCompletePage(),
          ),
        editprofile => GetPage(
            name: value,
            page: () => EditAccountPage(),
          ),
      };
}
