import 'package:flutter/material.dart';

class StyleRepo {
  static const green = Color(0xFF53B175);
  static const gray = Color(0xFFE2E2E2);
  static const white = Color(0xFFFCFCFC);
  static const transparent = Colors.transparent;
  static const lightGrey = Color(0xFFB1B1B1);
  static const darkGrey = Color(0xFF7C7C7C);
  static const black = Color(0xFF030303);
  static const skyblue = Color(0xFF53B175);
  static const red = Colors.red;
  static const blue = Colors.blue;
  static const yellow = Colors.amber;
  static const meduimGrey = Color(0xFFF2F3F2);
  static const sandyBrown = Color(0xFFF8A44C);

  // Meta Colors
  static const metaBlue = Color(0xFF0866FF);
  static const facebookBlue = Color(0xFF1877F2);
  static const metaLightBlue = Color(0xFF42A5F5);
  static const metaSkyBlue = Color(0xFF29B6F6);

  // Purple/Violet Colors
  static const purple = Color(0xFF8B5CF6);
  static const violet = Color(0xFF7C3AED);
  static const indigo = Color(0xFF6366F1);
  static const lightBlue = Color(0xFF3B82F6);

  // Light Meta Colors for backgrounds
  static const lightMetaBlue = Color(0xFFF0F4FF);
  static const veryLightMetaBlue = Color(0xFFF8F9FF);
  static const softMetaBlue = Color(0xFFE3EBFF);

  static const List<List<Color>> colorPairs = [
    [Color(0xB353B175), Color(0x1A53B175)],
    [Color(0xB3F8A44C), Color(0x1AF8A44C)],
    [Color(0xFFF7A593), Color(0x40F7A593)],
    [Color(0xFFD3B0E0), Color(0x40D3B0E0)],
    [Color(0xFFFDE598), Color(0x40FDE598)],
    [Color(0xFFB7DFF5), Color(0x40B7DFF5)],
  ];
}
