import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';
import 'package:shop_app/features/home/<USER>';

const double _kAspectRatio = 16 / 9;

class AdsWidget extends StatelessWidget {
  AdsWidget({super.key});

  final Rx<int> _currentAd = 0.obs;
  int get currentAd => _currentAd.value;
  set currentAd(int value) => _currentAd.value = value;
  final controller = Get.find<HomePageController>();
  @override
  Widget build(BuildContext context) {
    // List ads = [
    //   DemoMedia.getAppRandomImage,
    //   DemoMedia.getAppRandomImage,
    //   DemoMedia.getAppRandomImage,
    //   DemoMedia.getAppRandomImage,
    // ];
    return ObsListBuilder(
        obs: controller.products,
        builder: (context, products) {
          return Column(
            children: [
              CarouselSlider(
                options: CarouselOptions(
                  onPageChanged: (index, _) => currentAd = index,
                  autoPlay: true,
                  enlargeCenterPage: true,
                  aspectRatio: _kAspectRatio,
                ),
                items: List.generate(
                  products.length,
                  (index) => AspectRatio(
                    aspectRatio: _kAspectRatio,
                    child: AppImage(
                      width: double.infinity,
                      path: products[index].image,
                      type: ImageType.CachedNetwork,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                height: 12,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    products.length,
                    (index) => Obx(
                      () => AnimatedContainer(
                        duration: 300.milliseconds,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        height: currentAd == index ? 12 : 6,
                        width: currentAd == index ? 12 : 6,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: currentAd == index
                              ? StyleRepo.green
                              : StyleRepo.lightGrey,
                        ),
                      ),
                    ),
                  ),
                ),
              )
            ],
          );
        });
  }
}
