import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/EditAccount/controller.dart';
import 'package:shop_app/features/EditAccount/widgets/PickImage.dart';
import 'package:shop_app/features/EditAccount/widgets/TextFormEditProfile.dart';
import 'package:shop_app/gen/assets.gen.dart';

class EditAccountPage extends StatelessWidget {
  const EditAccountPage({super.key});
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(EditAccountPageController());

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: MediaQuery.of(context).size.height * .05,
        automaticallyImplyLeading: false,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(tr(LocaleKeys.Edit_profile),
                style: Theme.of(context).textTheme.titleMedium),
            IconButton(
              onPressed: () => Get.back(),
              icon: Assets.icons.back.svg(color: StyleRepo.black),
              iconSize: 24,
            ),
          ],
        ),
      ),
      body: Form(
        key: controller.formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16),
          children: [
            Center(child: PickImageMyProfile()),
            SizedBox(
              height: 12,
            ),
            Text(tr(LocaleKeys.User_name),
                style: Theme.of(context).textTheme.bodyLarge),
            const SizedBox(height: 6),
            TextFormEditProfile(
              controller: controller.name,
              icon: Assets.icons.profile.svg(),
              hintText: LocaleKeys.Name,
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                return null;
              },
            ),
            const SizedBox(height: 15),
            Text(tr(LocaleKeys.Email),
                style: Theme.of(context).textTheme.bodyLarge),
            const SizedBox(height: 6),
            TextFormEditProfile(
              controller: controller.email,
              icon: Assets.icons.message.svg(),
              hintText: LocaleKeys.user_name_gmail,
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                if (!value.contains("@gmail.com")) {
                  return tr(LocaleKeys.Wrong_email);
                }
                return null;
              },
            ),
            const SizedBox(height: 15),
            Text(tr(LocaleKeys.Phone_Number),
                style: Theme.of(context).textTheme.bodyLarge),
            const SizedBox(height: 6),
            TextFormEditProfile(
              controller: controller.phoneNumber,
              icon: Assets.icons.national.svg(),
              input: TextInputType.phone,
              hintText: LocaleKeys.hintNum,
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                if (!RegExp(r'^[0-9]+$').hasMatch(value))
                  return tr(LocaleKeys.only_numbers_allowed);

                return null;
              },
            ),
            const SizedBox(height: 15),
            Text(tr(LocaleKeys.Gender),
                style: Theme.of(context).textTheme.bodyLarge),
            const SizedBox(height: 6),
            DropdownButtonHideUnderline(
              child: ButtonTheme(
                alignedDropdown: true,
                child: DropdownButtonFormField<String>(
                  style: Theme.of(context).textTheme.bodyLarge,
                  value: controller.gender.text.isNotEmpty
                      ? controller.gender.text
                      : null,
                  onChanged: (value) {
                    if (value != null) {
                      controller.gender.text = value;
                    }
                  },
                  items: [
                    DropdownMenuItem(
                      value: tr(LocaleKeys.Male),
                      child: Text(tr(LocaleKeys.Male),
                          style: Theme.of(context).textTheme.bodyLarge),
                    ),
                    DropdownMenuItem(
                      value: tr(LocaleKeys.Female),
                      child: Text(tr(LocaleKeys.Female),
                          style: Theme.of(context).textTheme.bodyLarge),
                    ),
                  ],
                  decoration: InputDecoration(
                    prefixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(width: 8),
                        Assets.icons.gender.svg(),
                        const SizedBox(width: 8),
                        Assets.icons.line.svg(
                            color: StyleRepo.lightGrey, width: 16, height: 40),
                      ],
                    ),
                    hintText: tr(LocaleKeys.Gender),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 15,
            ),
            Text(tr(LocaleKeys.region),
                style: Theme.of(context).textTheme.bodyLarge),
            SizedBox(
              height: 6,
            ),
            TextFormEditProfile(
              controller: controller.region,
              icon: Assets.icons.location.svg(),
              hintText: LocaleKeys.region_name,
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                return null;
              },
            ),
            SizedBox(
              height: 15,
            ),
            Text(tr(LocaleKeys.country),
                style: Theme.of(context).textTheme.bodyLarge),
            SizedBox(
              height: 6,
            ),
            TextFormEditProfile(
              controller: controller.country,
              icon: Assets.icons.location.svg(),
              hintText: LocaleKeys.region_name,
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                return null;
              },
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: ElevatedButton(
                onPressed: controller.confirm,
                child: Text(
                  tr(LocaleKeys.save),
                  style: TextStyle(color: StyleRepo.white, fontSize: 16),
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            )
          ],
        ),
      ),
    );
  }
}
