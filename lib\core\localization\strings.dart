// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class LocaleKeys {
  static const shop = 'shop';
  static const home = 'home';
  static const cart = 'cart';
  static const explore = 'explore';
  static const favourite = 'favourite';
  static const account = 'account';
  static const see_all = 'see_all';
  static const Create_New_Account = 'Create_New_Account';
  static const add_your_information = 'add_your_information';
  static const User_Name = 'User_Name';
  static const Ex = 'Ex';
  static const This_field_is_required = 'This_field_is_required';
  static const Wrong_phone = 'Wrong_phone';
  static const Password = 'Password';
  static const add_strong_password = 'add_strong_password';
  static const Password_must_be = 'Password_must_be';
  static const Confirm_Password = 'Confirm_Password';
  static const Passwords_do_not_match = 'Passwords_do_not_match';
  static const Change_language = 'Change_language';
  static const Welcome_to = 'Welcome_to ';
  static const Login_to = 'Login_to';
  static const Enter_the_following = 'Enter_the_following';
  static const Forget_Passwords = 'Forget_Passwords';
  static const Join_guest = 'Join_guest';
  static const Login = 'Login';
  static const Sign_up = 'Sign_up';
  static const Confirm = 'Confirm';
  static const Verify_Phone_Number = 'Verify_Phone_Number';
  static const Enter_the_Code = 'Enter_the_Code';
  static const second = 'second';
  static const Resend_Code = 'Resend_Code';
  static const Back = 'Back';
  static const Complete_information = 'Complete_information';
  static const Add_your_information = 'Add_your_information';
  static const Add_Profile_Photo = 'Add_Profile_Photo';
  static const Add_Photo = 'Add_Photo';
  static const Full_Name = 'Full_Name';
  static const Email = 'Email';
  static const National_number = 'National_number';
  static const Gender = 'Gender';
  static const Male = 'Male';
  static const Female = 'Female';
  static const ID_photo = 'ID_photo';
  static const User_name = 'User_name';
  static const user_name_gmail = 'user_name_gmail';
  static const add_national_number = 'add_national_number';
  static const The_image_is_required = 'The_image_is_required';
  static const Wrong_email = 'Wrong_email';
  static const VALID = 'VALID';
  static const INVALID = 'INVALID';
  static const the_entered_code = 'the_entered_code';
  static const Categories = 'Categories';
  static const Products = 'Products';
  static const Find_Products = 'Find_Products';
  static const Search_Store = 'Search_Store';
  static const Product_Name = 'Product_Name';
  static const Product_Details = 'Product_Details';
  static const Add_To_Cart = 'Add_To_Cart';
  static const My_Cart = 'My_Cart';
  static const Cart_is_empty = 'Cart_is_empty';
  static const Deleted = 'Deleted';
  static const Removed_from_cart = 'Removed_from_cart';
  static const Done = 'Done';
  static const Cart_emptied = 'Cart_emptied';
  static const Empty_Cart = 'Empty_Cart';
  static const Price = 'Price';
  static const Go_to_Checkout = 'Go_to_Checkout';
  static const quntity = 'quntity';
  static const The_cart_will_be_emptied = 'The_cart_will_be_emptied';
  static const Are_You_Sure = 'Are_You_Sure';
  static const Yes = 'Yes';
  static const No = 'No';
  static const Orders = 'Orders';
  static const My_Details = 'My_Details';
  static const Delivery_Address = 'Delivery_Address';
  static const Payment_Methods = 'Payment_Methods';
  static const Promo_Code = 'Promo_Code';
  static const Notifications = 'Notifications';
  static const Help = 'Help';
  static const About = 'About';
  static const Log_Out = 'Log_Out';
  static const Name = 'Name';
  static const only_numbers_allowed = 'only_numbers_allowed';
  static const region = 'region';
  static const region_name = 'region_name';
  static const country = 'country';
  static const User_country = 'User_country';
  static const save = 'save';
  static const hintNum = 'hintNum';
  static const Edit_profile = 'Edit_profile';
  static const about_us = 'about_us';
  static const shop_app_name = 'shop_app_name';
  static const version = 'version';
  static const about_description = 'about_description';
  static const app_description = 'app_description';
  static const features = 'features';
  static const feature_easy_shopping = 'feature_easy_shopping';
  static const feature_secure_payment = 'feature_secure_payment';
  static const feature_fast_delivery = 'feature_fast_delivery';
  static const feature_customer_support = 'feature_customer_support';
  static const feature_product_variety = 'feature_product_variety';
  static const feature_user_friendly = 'feature_user_friendly';
  static const contact_us = 'contact_us';
  static const error = 'error';
  static const cannot_open_email = 'cannot_open_email';
  static const cannot_open_phone = 'cannot_open_phone';
  static const cannot_open_website = 'cannot_open_website';
  static const No_Results_Found = 'No_Results_Found';
  static const Try_Different_Keywords = 'Try_Different_Keywords';
  static const Rating = 'Rating';
  static const location_picker = 'location_picker';
  static const my_location = 'my_location';
  static const selected_location = 'selected_location';
  static const loading_location = 'loading_location';
  static const confirm_location = 'confirm_location';
  static const location_service_disabled = 'location_service_disabled';
  static const location_permission_denied = 'location_permission_denied';
  static const location_permission_denied_forever =
      'location_permission_denied_forever';
  static const location_error = 'location_error';
  static const marker_selected_location = 'marker_selected_location';
  static const marker_tap_to_confirm = 'marker_tap_to_confirm';
  static const location_not_specified = 'location_not_specified';
  static const address_not_found = 'address_not_found';
  static const address_error = 'address_error';
  static const success = 'success';
  static const location_saved = 'location_saved';
  static const language = 'language';
  static const english = 'english';
  static const arabic = 'arabic';
  static const language_changed = 'language_changed';
}
