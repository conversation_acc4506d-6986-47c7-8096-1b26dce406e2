import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/services/pagination/controller.dart';

import '../../../core/services/rest_api/rest_api.dart';

class ProductsPageController extends GetxController {
  late PaginationController pagerController;

  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.products,
        params: {"page": page},
        cancelToken: cancel,
      ),
    );
    return response;
  }

  refreshData() {
    pagerController.refreshData();
  }
}
